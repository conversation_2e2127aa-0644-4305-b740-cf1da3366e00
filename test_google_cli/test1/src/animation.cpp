#include "animation.h"
#include "logger.h"
#include <algorithm>
#include <cmath>
#include <glm/gtc/type_ptr.hpp>

AnimationManager::AnimationManager(const Gltf::GltfModel* gltfModel) : model(gltfModel) {
    if (!model) {
        LOG_ERROR("AnimationManager: null glTF model provided");
        throw std::invalid_argument("glTF model cannot be null");
    }

    LOG_INFO("AnimationManager: Initializing with " + std::to_string(model->nodes.size()) + " nodes");

    try {
        initializeNodeTransforms();
        LOG_INFO("AnimationManager: Node transforms initialized");

        // Validate animation data structures
        validateAnimationData();
        LOG_INFO("AnimationManager: Animation data validation completed");

        // PERFORMANCE OPTIMIZATION: Cache animation data once during initialization
        cacheAnimationData();
        LOG_INFO("AnimationManager: Animation data cached for performance");
    } catch (const std::exception& e) {
        LOG_ERROR("AnimationManager: Failed to initialize: " + std::string(e.what()));
        throw;
    }

    // Initialize bone matrices if there are skins
    if (!model->skins.empty()) {
        const Gltf::Skin& skin = model->skins[0];
        boneMatrices.resize(skin.joints.size(), glm::mat4(1.0f));
        inverseBindMatrices.resize(skin.joints.size(), glm::mat4(1.0f));

        LOG_INFO("Initializing " + std::to_string(skin.joints.size()) + " bone matrices");

        // Load inverse bind matrices for the first skin
        if (skin.inverseBindMatrices != -1) {
            const Gltf::Accessor& accessor = model->accessors[skin.inverseBindMatrices];
            const Gltf::BufferView& bufferView = model->bufferViews[accessor.bufferView];
            const Gltf::Buffer& buffer = model->buffers[bufferView.buffer];

            LOG_TRACE("Loading inverse bind matrices: accessor=" + std::to_string(skin.inverseBindMatrices) +
                     ", count=" + std::to_string(accessor.count) +
                     ", bufferView=" + std::to_string(accessor.bufferView));

            const float* matrices = reinterpret_cast<const float*>(
                buffer.data.data() + bufferView.byteOffset + accessor.byteOffset);

            // Validate buffer bounds
            size_t totalSize = bufferView.byteOffset + accessor.byteOffset + (accessor.count * 16 * sizeof(float));
            if (totalSize > buffer.data.size()) {
                LOG_ERROR("Inverse bind matrices buffer overflow! Required: " + std::to_string(totalSize) +
                         ", Available: " + std::to_string(buffer.data.size()));
                // Use identity matrices as fallback
                for (size_t i = 0; i < inverseBindMatrices.size(); ++i) {
                    inverseBindMatrices[i] = glm::mat4(1.0f);
                }
            } else {
                for (size_t i = 0; i < accessor.count && i < inverseBindMatrices.size(); ++i) {
                    // glTF matrices are column-major, same as glm
                    inverseBindMatrices[i] = glm::make_mat4(&matrices[i * 16]);

                    // Validate matrix for reasonable values
                    bool isValid = true;
                    for (int j = 0; j < 16; ++j) {
                        float val = matrices[i * 16 + j];
                        if (std::isnan(val) || std::isinf(val) || std::abs(val) > 1e6f) {
                            isValid = false;
                            break;
                        }
                    }

                    if (!isValid) {
                        LOG_ERROR("Invalid inverse bind matrix " + std::to_string(i) + " detected, using identity");
                        inverseBindMatrices[i] = glm::mat4(1.0f);
                    }

                    // Debug first matrix
                    if (i == 0) {
                        LOG_TRACE("Inverse bind matrix 0:");
                        for (int row = 0; row < 4; ++row) {
                            std::string rowStr = "  [";
                            for (int col = 0; col < 4; ++col) {
                                rowStr += std::to_string(matrices[i * 16 + col * 4 + row]) + ", ";
                            }
                            rowStr += "]";
                            LOG_TRACE(rowStr);
                        }
                    }
                }
            }
        } else {
            LOG_WARNING("No inverse bind matrices found, using identity matrices");
        }
    }
    
    // Initialize to bind pose (T-pose) by default
    setBindPose();

    LOG_INFO("AnimationManager initialized with " + std::to_string(model->animations.size()) + " animations");

    // Debug animation data
    for (size_t i = 0; i < model->animations.size(); ++i) {
        const auto& anim = model->animations[i];
        LOG_INFO("Animation " + std::to_string(i) + ": " + anim.name +
                 " (" + std::to_string(anim.channels.size()) + " channels, " +
                 std::to_string(anim.samplers.size()) + " samplers)");
    }
}

void AnimationManager::initializeNodeTransforms() {
    nodeTransforms.resize(model->nodes.size());
    
    for (size_t i = 0; i < model->nodes.size(); ++i) {
        const Gltf::Node& node = model->nodes[i];
        
        if (!node.matrix.empty()) {
            // If matrix is provided, decompose it
            glm::mat4 matrix = glm::make_mat4(node.matrix.data());
            // For simplicity, we'll just use the matrix as-is and extract basic transform
            nodeTransforms[i].translation = glm::vec3(matrix[3]);
            nodeTransforms[i].scale = glm::vec3(
                glm::length(glm::vec3(matrix[0])),
                glm::length(glm::vec3(matrix[1])),
                glm::length(glm::vec3(matrix[2]))
            );
            // Rotation extraction is more complex, for now use identity
            nodeTransforms[i].rotation = glm::quat(1.0f, 0.0f, 0.0f, 0.0f);
        } else {
            nodeTransforms[i].translation = node.translation;
            nodeTransforms[i].rotation = node.rotation;
            nodeTransforms[i].scale = node.scale;
        }
    }
}

void AnimationManager::playAnimation(int animationIndex, bool loop) {
    size_t animationCount = model->animations.size();
    LOG_INFO("playAnimation called: index=" + std::to_string(animationIndex) +
             ", animationCount=" + std::to_string(animationCount));

    if (animationCount == 0) {
        LOG_ERROR("Cannot play animation: no animations available in model");
        return;
    }

    if (animationIndex < 0 || animationIndex >= static_cast<int>(animationCount)) {
        LOG_ERROR("Invalid animation index: " + std::to_string(animationIndex) +
                  " (valid range: 0-" + std::to_string(animationCount - 1) + ")");
        return;
    }

    currentAnimation.animationIndex = animationIndex;
    currentAnimation.currentTime = 0.0f;
    currentAnimation.playing = true;
    currentAnimation.looping = loop;

    calculateAnimationDuration(animationIndex);

    LOG_INFO("Animation " + std::to_string(animationIndex) + " started successfully" +
             " (duration: " + std::to_string(currentAnimation.duration) + "s, playing: " +
             (currentAnimation.playing ? "true" : "false") + ")");
}

void AnimationManager::stopAnimation() {
    currentAnimation.playing = false;
    currentAnimation.currentTime = 0.0f;
}

void AnimationManager::pauseAnimation() {
    currentAnimation.playing = false;
    LOG_INFO("Animation paused at time: " + std::to_string(currentAnimation.currentTime) + "s");
}

void AnimationManager::resumeAnimation() {
    if (currentAnimation.animationIndex == -1) {
        LOG_WARNING("No animation to resume");
        return;
    }

    // Validate model pointer
    if (!model) {
        LOG_ERROR("Cannot resume animation: model pointer is null");
        currentAnimation.animationIndex = -1;
        currentAnimation.playing = false;
        return;
    }

    // Check if there are any animations available
    size_t animationCount = model->animations.size();
    LOG_INFO("Resume validation: animationIndex=" + std::to_string(currentAnimation.animationIndex) +
             ", animationCount=" + std::to_string(animationCount));

    if (animationCount == 0) {
        LOG_ERROR("Cannot resume animation: no animations available in model");
        currentAnimation.animationIndex = -1;
        currentAnimation.playing = false;
        return;
    }

    // Validate animation index before resuming
    if (currentAnimation.animationIndex < 0 || currentAnimation.animationIndex >= static_cast<int>(animationCount)) {
        LOG_ERROR("Cannot resume animation: invalid animation index " + std::to_string(currentAnimation.animationIndex) +
                  " (valid range: 0-" + std::to_string(animationCount - 1) + ")");
        currentAnimation.animationIndex = -1;
        currentAnimation.playing = false;
        return;
    }

    currentAnimation.playing = true;
    LOG_INFO("Animation resumed from time: " + std::to_string(currentAnimation.currentTime) + "s");
}

bool AnimationManager::isPlaying() const {
    return currentAnimation.playing;
}

void AnimationManager::calculateAnimationDuration(int animationIndex) {
    const Gltf::Animation& animation = model->animations[animationIndex];
    float maxTime = 0.0f;

    LOG_TRACE("Calculating duration for animation " + std::to_string(animationIndex) + " with " + std::to_string(animation.samplers.size()) + " samplers");

    for (size_t i = 0; i < animation.samplers.size(); ++i) {
        const auto& sampler = animation.samplers[i];
        LOG_TRACE("Sampler " + std::to_string(i) + ": input=" + std::to_string(sampler.input) + ", output=" + std::to_string(sampler.output));

        if (sampler.input >= 0 && sampler.input < static_cast<int>(model->accessors.size())) {
            const Gltf::Accessor& inputAccessor = model->accessors[sampler.input];
            std::vector<float> times = extractTimeData(inputAccessor);
            LOG_TRACE("Extracted " + std::to_string(times.size()) + " time values");

            if (!times.empty()) {
                float samplerMaxTime = times.back();
                LOG_TRACE("Sampler max time: " + std::to_string(samplerMaxTime));
                maxTime = std::max(maxTime, samplerMaxTime);
            }
        }
    }

    // If no valid duration found, use a default
    if (maxTime <= 0.0f) {
        maxTime = 2.0f; // Default 2 second animation
        LOG_WARNING("No valid animation duration found, using default: " + std::to_string(maxTime) + "s");
    }

    currentAnimation.duration = maxTime;
    LOG_INFO("Animation duration calculated: " + std::to_string(maxTime) + "s");
}

void AnimationManager::updateAnimation(float deltaTime) {
    if (!currentAnimation.playing || currentAnimation.animationIndex == -1) {
        return;
    }

    // Validate model pointer
    if (!model) {
        LOG_ERROR("Cannot update animation: model pointer is null");
        currentAnimation.playing = false;
        return;
    }

    // Validate animation index
    size_t animationCount = model->animations.size();
    if (animationCount == 0) {
        LOG_ERROR("Cannot update animation: no animations available in model");
        currentAnimation.playing = false;
        return;
    }

    if (currentAnimation.animationIndex < 0 || currentAnimation.animationIndex >= static_cast<int>(animationCount)) {
        LOG_ERROR("Invalid animation index: " + std::to_string(currentAnimation.animationIndex) +
                  " (valid range: 0-" + std::to_string(animationCount - 1) + ")");
        currentAnimation.playing = false;
        return;
    }

    currentAnimation.currentTime += deltaTime;

    // Handle looping
    if (currentAnimation.duration > 0.0f && currentAnimation.currentTime > currentAnimation.duration) {
        if (currentAnimation.looping) {
            currentAnimation.currentTime = std::fmod(currentAnimation.currentTime, currentAnimation.duration);
        } else {
            currentAnimation.currentTime = currentAnimation.duration;
            currentAnimation.playing = false;
        }
    }

    // Debug animation progress every second
    static float lastDebugTime = 0.0f;
    if (currentAnimation.currentTime - lastDebugTime > 1.0f) {
        LOG_TRACE("Animation progress: " + std::to_string(currentAnimation.currentTime) + "s / " + std::to_string(currentAnimation.duration) + "s");
        lastDebugTime = currentAnimation.currentTime;
    }
    
    // PERFORMANCE OPTIMIZATION: Use cached animation data instead of extracting every frame
    if (!animationDataCached || currentAnimation.animationIndex >= static_cast<int>(cachedAnimationData.size())) {
        LOG_ERROR("Animation data not cached or invalid animation index");
        return;
    }

    const std::vector<CachedChannelData>& channels = cachedAnimationData[currentAnimation.animationIndex];

    // PERFORMANCE OPTIMIZATION: Only update if animation time has changed significantly
    const float timeThreshold = 0.001f; // 1ms threshold
    if (std::abs(currentAnimation.currentTime - lastAnimationTime) < timeThreshold) {
        return; // Skip update if time hasn't changed enough
    }

    bool animationChanged = false;

    for (size_t channelIndex = 0; channelIndex < channels.size(); ++channelIndex) {
        const CachedChannelData& cachedData = channels[channelIndex];

        if (cachedData.targetNode == -1 || cachedData.targetNode >= static_cast<int>(nodeTransforms.size())) {
            continue; // Skip invalid nodes
        }

        if (cachedData.times.empty()) {
            continue; // Skip channels with no time data
        }

        // PERFORMANCE OPTIMIZATION: Use cached data instead of extracting every frame
        if (cachedData.targetPath == "translation") {
            if (!cachedData.vec3Values.empty() && cachedData.times.size() == cachedData.vec3Values.size()) {
                glm::vec3 newTranslation = interpolateVec3(cachedData.times, cachedData.vec3Values,
                                                         currentAnimation.currentTime, cachedData.interpolation);
                if (nodeTransforms[cachedData.targetNode].translation != newTranslation) {
                    nodeTransforms[cachedData.targetNode].translation = newTranslation;
                    animationChanged = true;
                }
            }
        } else if (cachedData.targetPath == "rotation") {
            if (!cachedData.quatValues.empty() && cachedData.times.size() == cachedData.quatValues.size()) {
                glm::quat newRotation = interpolateQuat(cachedData.times, cachedData.quatValues,
                                                       currentAnimation.currentTime, cachedData.interpolation);
                if (nodeTransforms[cachedData.targetNode].rotation != newRotation) {
                    nodeTransforms[cachedData.targetNode].rotation = newRotation;
                    animationChanged = true;
                }
            }
        } else if (cachedData.targetPath == "scale") {
            if (!cachedData.vec3Values.empty() && cachedData.times.size() == cachedData.vec3Values.size()) {
                glm::vec3 newScale = interpolateVec3(cachedData.times, cachedData.vec3Values,
                                                    currentAnimation.currentTime, cachedData.interpolation);
                if (nodeTransforms[cachedData.targetNode].scale != newScale) {
                    nodeTransforms[cachedData.targetNode].scale = newScale;
                    animationChanged = true;
                }
            }
        }
    }

    // PERFORMANCE OPTIMIZATION: Only update bone matrices if animation actually changed
    if (animationChanged || boneMatricesNeedUpdate) {
        if (!model->skins.empty()) {
            updateBoneMatrices(0); // Use first skin for now
            boneMatricesNeedUpdate = false;
        }
    }

    // Update last animation time for next frame comparison
    lastAnimationTime = currentAnimation.currentTime;
}

glm::mat4 AnimationManager::getNodeMatrix(int nodeIndex) const {
    if (nodeIndex < 0 || nodeIndex >= static_cast<int>(nodeTransforms.size())) {
        return glm::mat4(1.0f);
    }
    
    return nodeTransforms[nodeIndex].getMatrix();
}

void AnimationManager::updateBoneMatrices(int skinIndex) {
    if (skinIndex < 0 || skinIndex >= static_cast<int>(model->skins.size())) {
        return;
    }

    const Gltf::Skin& skin = model->skins[skinIndex];

    // Calculate global transforms for all nodes using proper hierarchy traversal
    std::vector<glm::mat4> globalTransforms(model->nodes.size(), glm::mat4(1.0f));

    // Find root nodes (nodes that are not children of any other node) and calculate their transforms
    std::vector<bool> isChild(model->nodes.size(), false);
    for (const auto& node : model->nodes) {
        for (int childIndex : node.children) {
            if (childIndex >= 0 && childIndex < static_cast<int>(isChild.size())) {
                isChild[childIndex] = true;
            }
        }
    }

    // Calculate transforms starting from root nodes
    for (size_t i = 0; i < model->nodes.size(); ++i) {
        if (!isChild[i]) {
            // This is a root node
            calculateGlobalTransforms(static_cast<int>(i), glm::mat4(1.0f), globalTransforms);
        }
    }

    for (size_t i = 0; i < skin.joints.size() && i < boneMatrices.size(); ++i) {
        int jointIndex = skin.joints[i];

        if (jointIndex >= 0 && jointIndex < static_cast<int>(globalTransforms.size())) {
            glm::mat4 globalTransform = globalTransforms[jointIndex];

            // PERFORMANCE OPTIMIZATION: Removed excessive TRACE logging
            // Apply inverse bind matrix: finalMatrix = globalTransform * inverseBindMatrix
            if (i < inverseBindMatrices.size()) {

                boneMatrices[i] = globalTransform * inverseBindMatrices[i];

                // Safety check for invalid values
                bool hasInvalidValues = false;
                for (int row = 0; row < 4; ++row) {
                    for (int col = 0; col < 4; ++col) {
                        float val = boneMatrices[i][col][row];
                        if (std::isnan(val) || std::isinf(val) || std::abs(val) > 1e6f) {
                            hasInvalidValues = true;
                            break;
                        }
                    }
                    if (hasInvalidValues) break;
                }

                if (hasInvalidValues) {
                    LOG_ERROR("Invalid bone matrix detected for joint " + std::to_string(jointIndex) + " - SKELETAL ANIMATION SYSTEM FAILURE");
                    LOG_ERROR("This indicates corrupted inverse bind matrices or node transforms");
                    LOG_ERROR("Global transform and inverse bind matrix shown above");
                    boneMatrices[i] = glm::mat4(1.0f);
                }
            } else {
                boneMatrices[i] = globalTransform;
            }
        } else {
            boneMatrices[i] = glm::mat4(1.0f);
        }
    }

    // PERFORMANCE OPTIMIZATION: Reduced logging frequency
    static int logCounter = 0;
    if (++logCounter % 60 == 0) { // Log every 60 frames (~1 second at 60fps)
        LOG_INFO("Updated " + std::to_string(skin.joints.size()) + " bone matrices");
    }
}

void AnimationManager::calculateGlobalTransforms(int nodeIndex, const glm::mat4& parentTransform, std::vector<glm::mat4>& globalTransforms) const {
    if (nodeIndex < 0 || nodeIndex >= static_cast<int>(model->nodes.size())) {
        LOG_ERROR("Invalid node index in calculateGlobalTransforms: " + std::to_string(nodeIndex));
        return;
    }

    const Gltf::Node& node = model->nodes[nodeIndex];

    // CRITICAL FIX: Use original node transform if no animation exists
    glm::mat4 localTransform;

    // Check if this node has animation data
    bool hasAnimation = (nodeIndex < static_cast<int>(nodeTransforms.size()) &&
                        !nodeTransforms[nodeIndex].isIdentity());

    if (hasAnimation) {
        // Use animated transform
        localTransform = nodeTransforms[nodeIndex].getMatrix();
    } else {
        // CRITICAL: Use original glTF node transform as fallback
        localTransform = getOriginalNodeTransform(node);
    }

    // PERFORMANCE OPTIMIZATION: Removed excessive TRACE logging

    // Calculate global transform
    globalTransforms[nodeIndex] = parentTransform * localTransform;

    // Check for invalid values in global transform
    bool hasInvalidGlobal = false;
    for (int row = 0; row < 4; ++row) {
        for (int col = 0; col < 4; ++col) {
            float val = globalTransforms[nodeIndex][col][row];
            if (std::isnan(val) || std::isinf(val) || std::abs(val) > 1e6f) {
                hasInvalidGlobal = true;
                break;
            }
        }
        if (hasInvalidGlobal) break;
    }

    if (hasInvalidGlobal) {
        LOG_ERROR("Invalid global transform calculated for node " + std::to_string(nodeIndex));
        globalTransforms[nodeIndex] = glm::mat4(1.0f);
    }

    // Recursively calculate for children
    for (int childIndex : node.children) {
        calculateGlobalTransforms(childIndex, globalTransforms[nodeIndex], globalTransforms);
    }
}

std::vector<float> AnimationManager::extractTimeData(const Gltf::Accessor& accessor) const {
    // Validate accessor indices
    if (accessor.bufferView < 0 || accessor.bufferView >= static_cast<int>(model->bufferViews.size())) {
        LOG_ERROR("Invalid bufferView index: " + std::to_string(accessor.bufferView));
        return std::vector<float>{0.0f, 1.0f};
    }

    const Gltf::BufferView& bufferView = model->bufferViews[accessor.bufferView];

    if (bufferView.buffer < 0 || bufferView.buffer >= static_cast<int>(model->buffers.size())) {
        LOG_ERROR("Invalid buffer index: " + std::to_string(bufferView.buffer));
        return std::vector<float>{0.0f, 1.0f};
    }

    const Gltf::Buffer& buffer = model->buffers[bufferView.buffer];

    // Validate buffer bounds
    size_t totalSize = bufferView.byteOffset + accessor.byteOffset + (accessor.count * sizeof(float));
    if (totalSize > buffer.data.size()) {
        LOG_ERROR("Time data buffer overflow! Required: " + std::to_string(totalSize) +
                 ", Available: " + std::to_string(buffer.data.size()));
        return std::vector<float>{0.0f, 1.0f}; // Return default time range
    }

    const float* data = reinterpret_cast<const float*>(
        buffer.data.data() + bufferView.byteOffset + accessor.byteOffset);

    std::vector<float> times;
    times.reserve(accessor.count);

    // Validate and copy time data
    for (size_t i = 0; i < accessor.count; ++i) {
        float timeValue = data[i];

        // Validate time value
        if (std::isnan(timeValue) || std::isinf(timeValue) || timeValue < 0.0f) {
            LOG_ERROR("Invalid time value at index " + std::to_string(i) + ": " + std::to_string(timeValue));
            timeValue = static_cast<float>(i); // Use index as fallback
        }

        times.push_back(timeValue);
    }

    // Debug the extracted time data
    LOG_TRACE("Extracted time data: count=" + std::to_string(accessor.count) +
              ", bufferView=" + std::to_string(accessor.bufferView) +
              ", byteOffset=" + std::to_string(accessor.byteOffset));

    for (size_t i = 0; i < std::min(times.size(), size_t(5)); ++i) {
        LOG_TRACE("Time[" + std::to_string(i) + "] = " + std::to_string(times[i]));
    }

    return times;
}

std::vector<glm::vec3> AnimationManager::extractVec3Data(const Gltf::Accessor& accessor) const {
    // Validate accessor indices
    if (accessor.bufferView < 0 || accessor.bufferView >= static_cast<int>(model->bufferViews.size())) {
        LOG_ERROR("Invalid bufferView index in extractVec3Data: " + std::to_string(accessor.bufferView));
        return std::vector<glm::vec3>{glm::vec3(0.0f)};
    }

    const Gltf::BufferView& bufferView = model->bufferViews[accessor.bufferView];

    if (bufferView.buffer < 0 || bufferView.buffer >= static_cast<int>(model->buffers.size())) {
        LOG_ERROR("Invalid buffer index in extractVec3Data: " + std::to_string(bufferView.buffer));
        return std::vector<glm::vec3>{glm::vec3(0.0f)};
    }

    const Gltf::Buffer& buffer = model->buffers[bufferView.buffer];

    // Validate buffer bounds for Vec3 data (3 floats per element)
    size_t totalSize = bufferView.byteOffset + accessor.byteOffset + (accessor.count * 3 * sizeof(float));
    if (totalSize > buffer.data.size()) {
        LOG_ERROR("Vec3 data buffer overflow! Required: " + std::to_string(totalSize) +
                 ", Available: " + std::to_string(buffer.data.size()));
        return std::vector<glm::vec3>{glm::vec3(0.0f)}; // Return default value
    }

    const float* data = reinterpret_cast<const float*>(
        buffer.data.data() + bufferView.byteOffset + accessor.byteOffset);

    std::vector<glm::vec3> result;
    result.reserve(accessor.count);

    // Validate and copy Vec3 data
    for (size_t i = 0; i < accessor.count; ++i) {
        float x = data[i * 3];
        float y = data[i * 3 + 1];
        float z = data[i * 3 + 2];

        // Validate values for NaN/Inf
        if (std::isnan(x) || std::isinf(x) || std::isnan(y) || std::isinf(y) || std::isnan(z) || std::isinf(z)) {
            LOG_ERROR("Invalid Vec3 values at index " + std::to_string(i) + ": (" +
                     std::to_string(x) + ", " + std::to_string(y) + ", " + std::to_string(z) + ")");
            result.emplace_back(0.0f, 0.0f, 0.0f); // Use zero vector as fallback
        } else {
            result.emplace_back(x, y, z);
        }
    }

    LOG_TRACE("Extracted " + std::to_string(result.size()) + " Vec3 values from accessor");
    return result;
}

std::vector<glm::quat> AnimationManager::extractQuatData(const Gltf::Accessor& accessor) const {
    // Validate accessor indices
    if (accessor.bufferView < 0 || accessor.bufferView >= static_cast<int>(model->bufferViews.size())) {
        LOG_ERROR("Invalid bufferView index in extractQuatData: " + std::to_string(accessor.bufferView));
        return std::vector<glm::quat>{glm::quat(1.0f, 0.0f, 0.0f, 0.0f)};
    }

    const Gltf::BufferView& bufferView = model->bufferViews[accessor.bufferView];

    if (bufferView.buffer < 0 || bufferView.buffer >= static_cast<int>(model->buffers.size())) {
        LOG_ERROR("Invalid buffer index in extractQuatData: " + std::to_string(bufferView.buffer));
        return std::vector<glm::quat>{glm::quat(1.0f, 0.0f, 0.0f, 0.0f)};
    }

    const Gltf::Buffer& buffer = model->buffers[bufferView.buffer];

    // Validate buffer bounds for Quaternion data (4 floats per element)
    size_t totalSize = bufferView.byteOffset + accessor.byteOffset + (accessor.count * 4 * sizeof(float));
    if (totalSize > buffer.data.size()) {
        LOG_ERROR("Quaternion data buffer overflow! Required: " + std::to_string(totalSize) +
                 ", Available: " + std::to_string(buffer.data.size()));
        return std::vector<glm::quat>{glm::quat(1.0f, 0.0f, 0.0f, 0.0f)}; // Return identity quaternion
    }

    const float* data = reinterpret_cast<const float*>(
        buffer.data.data() + bufferView.byteOffset + accessor.byteOffset);

    std::vector<glm::quat> result;
    result.reserve(accessor.count);

    // Validate and copy Quaternion data
    for (size_t i = 0; i < accessor.count; ++i) {
        float x = data[i * 4];
        float y = data[i * 4 + 1];
        float z = data[i * 4 + 2];
        float w = data[i * 4 + 3];

        // Validate values for NaN/Inf
        if (std::isnan(x) || std::isinf(x) || std::isnan(y) || std::isinf(y) ||
            std::isnan(z) || std::isinf(z) || std::isnan(w) || std::isinf(w)) {
            LOG_ERROR("Invalid Quaternion values at index " + std::to_string(i) + ": (" +
                     std::to_string(x) + ", " + std::to_string(y) + ", " + std::to_string(z) + ", " + std::to_string(w) + ")");
            result.emplace_back(1.0f, 0.0f, 0.0f, 0.0f); // Use identity quaternion as fallback
        } else {
            // glTF quaternions are stored as [x, y, z, w], glm expects [w, x, y, z]
            result.emplace_back(w, x, y, z);
        }
    }

    LOG_TRACE("Extracted " + std::to_string(result.size()) + " Quaternion values from accessor");
    return result;
}

size_t AnimationManager::findKeyframeIndex(const std::vector<float>& times, float time) const {
    auto it = std::lower_bound(times.begin(), times.end(), time);
    if (it == times.end()) {
        return times.size() - 1;
    }
    if (it == times.begin()) {
        return 0;
    }
    return std::distance(times.begin(), it) - 1;
}

float AnimationManager::clamp(float value, float min, float max) const {
    return std::max(min, std::min(max, value));
}

glm::vec3 AnimationManager::interpolateVec3(const std::vector<float>& times, const std::vector<glm::vec3>& values, float time, const std::string& interpolation) {
    if (times.empty() || values.empty()) {
        return glm::vec3(0.0f);
    }

    if (times.size() == 1 || time <= times[0]) {
        return values[0];
    }

    if (time >= times.back()) {
        return values.back();
    }

    size_t index = findKeyframeIndex(times, time);
    if (index >= times.size() - 1) {
        return values.back();
    }

    float t0 = times[index];
    float t1 = times[index + 1];
    float factor = (time - t0) / (t1 - t0);

    if (interpolation == "STEP") {
        return values[index];
    } else if (interpolation == "LINEAR") {
        return glm::mix(values[index], values[index + 1], factor);
    } else if (interpolation == "CUBICSPLINE") {
        // Simplified cubic spline interpolation
        // For full implementation, we'd need tangent vectors
        return glm::mix(values[index], values[index + 1], factor);
    }

    return glm::mix(values[index], values[index + 1], factor);
}

glm::quat AnimationManager::interpolateQuat(const std::vector<float>& times, const std::vector<glm::quat>& values, float time, const std::string& interpolation) {
    if (times.empty() || values.empty()) {
        return glm::quat(1.0f, 0.0f, 0.0f, 0.0f);
    }

    if (times.size() == 1 || time <= times[0]) {
        return values[0];
    }

    if (time >= times.back()) {
        return values.back();
    }

    size_t index = findKeyframeIndex(times, time);
    if (index >= times.size() - 1) {
        return values.back();
    }

    float t0 = times[index];
    float t1 = times[index + 1];
    float factor = (time - t0) / (t1 - t0);

    if (interpolation == "STEP") {
        return values[index];
    } else {
        // Use spherical linear interpolation for quaternions
        return glm::slerp(values[index], values[index + 1], factor);
    }
}

// Helper method to get original node transform from glTF data
glm::mat4 AnimationManager::getOriginalNodeTransform(const Gltf::Node& node) const {
    // If node has a matrix directly, use that instead
    if (!node.matrix.empty() && node.matrix.size() >= 16) {
        // glTF matrices are column-major
        return glm::mat4(
            node.matrix[0], node.matrix[1], node.matrix[2], node.matrix[3],
            node.matrix[4], node.matrix[5], node.matrix[6], node.matrix[7],
            node.matrix[8], node.matrix[9], node.matrix[10], node.matrix[11],
            node.matrix[12], node.matrix[13], node.matrix[14], node.matrix[15]
        );
    }

    // Build transform matrix from TRS components (glm types)
    glm::mat4 transform = glm::mat4(1.0f);

    // Apply translation (glm::vec3)
    transform = glm::translate(transform, node.translation);

    // Apply rotation (glm::quat)
    transform = transform * glm::mat4_cast(node.rotation);

    // Apply scale (glm::vec3)
    transform = glm::scale(transform, node.scale);

    return transform;
}

void AnimationManager::setBindPose() {
    LOG_INFO("Setting model to bind pose (T-pose)");

    // Reset all node transforms to their original values
    for (size_t i = 0; i < nodeTransforms.size(); ++i) {
        if (i < model->nodes.size()) {
            const Gltf::Node& node = model->nodes[i];
            nodeTransforms[i].translation = node.translation;
            nodeTransforms[i].rotation = node.rotation;
            nodeTransforms[i].scale = node.scale;
        }
    }

    // Update bone matrices to bind pose
    if (!model->skins.empty()) {
        updateBoneMatrices(0);
        LOG_INFO("Bind pose set with " + std::to_string(boneMatrices.size()) + " bone matrices");
    }
}

void AnimationManager::validateAnimationData() {
    LOG_INFO("Validating animation data structures...");

    for (size_t animIndex = 0; animIndex < model->animations.size(); ++animIndex) {
        const Gltf::Animation& animation = model->animations[animIndex];
        LOG_TRACE("Validating animation " + std::to_string(animIndex) + " with " +
                 std::to_string(animation.channels.size()) + " channels and " +
                 std::to_string(animation.samplers.size()) + " samplers");

        // Validate samplers
        for (size_t samplerIndex = 0; samplerIndex < animation.samplers.size(); ++samplerIndex) {
            const Gltf::AnimationSampler& sampler = animation.samplers[samplerIndex];

            // Validate input accessor
            if (sampler.input < 0 || sampler.input >= static_cast<int>(model->accessors.size())) {
                LOG_ERROR("Animation " + std::to_string(animIndex) + " sampler " + std::to_string(samplerIndex) +
                         " has invalid input accessor: " + std::to_string(sampler.input));
                continue;
            }

            // Validate output accessor
            if (sampler.output < 0 || sampler.output >= static_cast<int>(model->accessors.size())) {
                LOG_ERROR("Animation " + std::to_string(animIndex) + " sampler " + std::to_string(samplerIndex) +
                         " has invalid output accessor: " + std::to_string(sampler.output));
                continue;
            }

            // Test data extraction to catch buffer issues early
            const Gltf::Accessor& inputAccessor = model->accessors[sampler.input];
            const Gltf::Accessor& outputAccessor = model->accessors[sampler.output];

            std::vector<float> times = extractTimeData(inputAccessor);
            if (times.empty()) {
                LOG_ERROR("Animation " + std::to_string(animIndex) + " sampler " + std::to_string(samplerIndex) +
                         " failed to extract time data");
                continue;
            }

            LOG_TRACE("Sampler " + std::to_string(samplerIndex) + " time range: " +
                     std::to_string(times.front()) + " to " + std::to_string(times.back()) +
                     " (" + std::to_string(times.size()) + " keyframes)");
        }

        // Validate channels
        for (size_t channelIndex = 0; channelIndex < animation.channels.size(); ++channelIndex) {
            const Gltf::AnimationChannel& channel = animation.channels[channelIndex];

            // Validate sampler reference
            if (channel.sampler < 0 || channel.sampler >= static_cast<int>(animation.samplers.size())) {
                LOG_ERROR("Animation " + std::to_string(animIndex) + " channel " + std::to_string(channelIndex) +
                         " has invalid sampler reference: " + std::to_string(channel.sampler));
                continue;
            }

            // Validate target node
            if (channel.target.node < 0 || channel.target.node >= static_cast<int>(model->nodes.size())) {
                LOG_ERROR("Animation " + std::to_string(animIndex) + " channel " + std::to_string(channelIndex) +
                         " has invalid target node: " + std::to_string(channel.target.node));
                continue;
            }

            // Validate target path
            if (channel.target.path != "translation" && channel.target.path != "rotation" &&
                channel.target.path != "scale" && channel.target.path != "weights") {
                LOG_WARNING("Animation " + std::to_string(animIndex) + " channel " + std::to_string(channelIndex) +
                           " has unsupported target path: " + channel.target.path);
            }

            LOG_TRACE("Channel " + std::to_string(channelIndex) + " targets node " +
                     std::to_string(channel.target.node) + " path '" + channel.target.path + "'");
        }
    }

    LOG_INFO("Animation data validation completed successfully");
}

void AnimationManager::cacheAnimationData() {
    if (animationDataCached || !model || model->animations.empty()) {
        return;
    }

    LOG_INFO("Caching animation data for " + std::to_string(model->animations.size()) + " animations");

    cachedAnimationData.resize(model->animations.size());

    for (size_t animIndex = 0; animIndex < model->animations.size(); ++animIndex) {
        const Gltf::Animation& animation = model->animations[animIndex];
        cachedAnimationData[animIndex].resize(animation.channels.size());

        for (size_t channelIndex = 0; channelIndex < animation.channels.size(); ++channelIndex) {
            const Gltf::AnimationChannel& channel = animation.channels[channelIndex];
            const Gltf::AnimationSampler& sampler = animation.samplers[channel.sampler];

            CachedChannelData& cachedData = cachedAnimationData[animIndex][channelIndex];

            // Cache basic channel info
            cachedData.interpolation = sampler.interpolation;
            cachedData.targetNode = channel.target.node;
            cachedData.targetPath = channel.target.path;

            // Cache time data (input accessor)
            const Gltf::Accessor& inputAccessor = model->accessors[sampler.input];
            cachedData.times = extractTimeData(inputAccessor);

            // Cache value data based on target path (output accessor)
            const Gltf::Accessor& outputAccessor = model->accessors[sampler.output];
            if (channel.target.path == "translation" || channel.target.path == "scale") {
                cachedData.vec3Values = extractVec3Data(outputAccessor);
            } else if (channel.target.path == "rotation") {
                cachedData.quatValues = extractQuatData(outputAccessor);
            }
        }
    }

    animationDataCached = true;
    LOG_INFO("Animation data caching completed");
}
