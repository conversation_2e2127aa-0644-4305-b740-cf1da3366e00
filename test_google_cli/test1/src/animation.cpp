#include "animation.h"
#include "logger.h"
#include <algorithm>
#include <cmath>
#include <glm/gtc/type_ptr.hpp>

AnimationManager::AnimationManager(const Gltf::GltfModel* gltfModel) : model(gltfModel) {
    if (!model) {
        LOG_ERROR("AnimationManager: null glTF model provided");
        throw std::invalid_argument("glTF model cannot be null");
    }

    LOG_INFO("AnimationManager: Initializing with " + std::to_string(model->nodes.size()) + " nodes");

    try {
        initializeNodeTransforms();
        LOG_INFO("AnimationManager: Node transforms initialized");
    } catch (const std::exception& e) {
        LOG_ERROR("AnimationManager: Failed to initialize node transforms: " + std::string(e.what()));
        throw;
    }

    // Initialize bone matrices if there are skins
    if (!model->skins.empty()) {
        const Gltf::Skin& skin = model->skins[0];
        boneMatrices.resize(skin.joints.size(), glm::mat4(1.0f));
        inverseBindMatrices.resize(skin.joints.size(), glm::mat4(1.0f));

        LOG_INFO("Initializing " + std::to_string(skin.joints.size()) + " bone matrices");

        // Load inverse bind matrices for the first skin
        if (skin.inverseBindMatrices != -1) {
            const Gltf::Accessor& accessor = model->accessors[skin.inverseBindMatrices];
            const Gltf::BufferView& bufferView = model->bufferViews[accessor.bufferView];
            const Gltf::Buffer& buffer = model->buffers[bufferView.buffer];

            LOG_TRACE("Loading inverse bind matrices: accessor=" + std::to_string(skin.inverseBindMatrices) +
                     ", count=" + std::to_string(accessor.count) +
                     ", bufferView=" + std::to_string(accessor.bufferView));

            const float* matrices = reinterpret_cast<const float*>(
                buffer.data.data() + bufferView.byteOffset + accessor.byteOffset);

            // Validate buffer bounds
            size_t totalSize = bufferView.byteOffset + accessor.byteOffset + (accessor.count * 16 * sizeof(float));
            if (totalSize > buffer.data.size()) {
                LOG_ERROR("Inverse bind matrices buffer overflow! Required: " + std::to_string(totalSize) +
                         ", Available: " + std::to_string(buffer.data.size()));
                // Use identity matrices as fallback
                for (size_t i = 0; i < inverseBindMatrices.size(); ++i) {
                    inverseBindMatrices[i] = glm::mat4(1.0f);
                }
            } else {
                for (size_t i = 0; i < accessor.count && i < inverseBindMatrices.size(); ++i) {
                    // glTF matrices are column-major, same as glm
                    inverseBindMatrices[i] = glm::make_mat4(&matrices[i * 16]);

                    // Validate matrix for reasonable values
                    bool isValid = true;
                    for (int j = 0; j < 16; ++j) {
                        float val = matrices[i * 16 + j];
                        if (std::isnan(val) || std::isinf(val) || std::abs(val) > 1e6f) {
                            isValid = false;
                            break;
                        }
                    }

                    if (!isValid) {
                        LOG_ERROR("Invalid inverse bind matrix " + std::to_string(i) + " detected, using identity");
                        inverseBindMatrices[i] = glm::mat4(1.0f);
                    }

                    // Debug first matrix
                    if (i == 0) {
                        LOG_TRACE("Inverse bind matrix 0:");
                        for (int row = 0; row < 4; ++row) {
                            std::string rowStr = "  [";
                            for (int col = 0; col < 4; ++col) {
                                rowStr += std::to_string(matrices[i * 16 + col * 4 + row]) + ", ";
                            }
                            rowStr += "]";
                            LOG_TRACE(rowStr);
                        }
                    }
                }
            }
        } else {
            LOG_WARNING("No inverse bind matrices found, using identity matrices");
        }
    }
    
    // Initialize to bind pose (T-pose) by default
    setBindPose();

    Utils::Logger::getInstance().log(Utils::Logger::INFO,
        "AnimationManager initialized with " + std::to_string(model->animations.size()) + " animations");
}

void AnimationManager::initializeNodeTransforms() {
    nodeTransforms.resize(model->nodes.size());
    
    for (size_t i = 0; i < model->nodes.size(); ++i) {
        const Gltf::Node& node = model->nodes[i];
        
        if (!node.matrix.empty()) {
            // If matrix is provided, decompose it
            glm::mat4 matrix = glm::make_mat4(node.matrix.data());
            // For simplicity, we'll just use the matrix as-is and extract basic transform
            nodeTransforms[i].translation = glm::vec3(matrix[3]);
            nodeTransforms[i].scale = glm::vec3(
                glm::length(glm::vec3(matrix[0])),
                glm::length(glm::vec3(matrix[1])),
                glm::length(glm::vec3(matrix[2]))
            );
            // Rotation extraction is more complex, for now use identity
            nodeTransforms[i].rotation = glm::quat(1.0f, 0.0f, 0.0f, 0.0f);
        } else {
            nodeTransforms[i].translation = node.translation;
            nodeTransforms[i].rotation = node.rotation;
            nodeTransforms[i].scale = node.scale;
        }
    }
}

void AnimationManager::playAnimation(int animationIndex, bool loop) {
    if (animationIndex < 0 || animationIndex >= static_cast<int>(model->animations.size())) {
        Utils::Logger::getInstance().log(Utils::Logger::WARNING, 
            "Invalid animation index: " + std::to_string(animationIndex));
        return;
    }
    
    currentAnimation.animationIndex = animationIndex;
    currentAnimation.currentTime = 0.0f;
    currentAnimation.playing = true;
    currentAnimation.looping = loop;
    
    calculateAnimationDuration(animationIndex);
    
    Utils::Logger::getInstance().log(Utils::Logger::INFO, 
        "Playing animation " + std::to_string(animationIndex) + 
        " (duration: " + std::to_string(currentAnimation.duration) + "s)");
}

void AnimationManager::stopAnimation() {
    currentAnimation.playing = false;
    currentAnimation.currentTime = 0.0f;
}

void AnimationManager::pauseAnimation() {
    currentAnimation.playing = false;
    LOG_INFO("Animation paused at time: " + std::to_string(currentAnimation.currentTime) + "s");
}

void AnimationManager::resumeAnimation() {
    if (currentAnimation.animationIndex != -1) {
        currentAnimation.playing = true;
        LOG_INFO("Animation resumed from time: " + std::to_string(currentAnimation.currentTime) + "s");
    } else {
        LOG_WARNING("No animation to resume");
    }
}

bool AnimationManager::isPlaying() const {
    return currentAnimation.playing;
}

void AnimationManager::calculateAnimationDuration(int animationIndex) {
    const Gltf::Animation& animation = model->animations[animationIndex];
    float maxTime = 0.0f;

    LOG_TRACE("Calculating duration for animation " + std::to_string(animationIndex) + " with " + std::to_string(animation.samplers.size()) + " samplers");

    for (size_t i = 0; i < animation.samplers.size(); ++i) {
        const auto& sampler = animation.samplers[i];
        LOG_TRACE("Sampler " + std::to_string(i) + ": input=" + std::to_string(sampler.input) + ", output=" + std::to_string(sampler.output));

        if (sampler.input >= 0 && sampler.input < static_cast<int>(model->accessors.size())) {
            const Gltf::Accessor& inputAccessor = model->accessors[sampler.input];
            std::vector<float> times = extractTimeData(inputAccessor);
            LOG_TRACE("Extracted " + std::to_string(times.size()) + " time values");

            if (!times.empty()) {
                float samplerMaxTime = times.back();
                LOG_TRACE("Sampler max time: " + std::to_string(samplerMaxTime));
                maxTime = std::max(maxTime, samplerMaxTime);
            }
        }
    }

    // If no valid duration found, use a default
    if (maxTime <= 0.0f) {
        maxTime = 2.0f; // Default 2 second animation
        LOG_WARNING("No valid animation duration found, using default: " + std::to_string(maxTime) + "s");
    }

    currentAnimation.duration = maxTime;
    LOG_INFO("Animation duration calculated: " + std::to_string(maxTime) + "s");
}

void AnimationManager::updateAnimation(float deltaTime) {
    if (!currentAnimation.playing || currentAnimation.animationIndex == -1) {
        return;
    }

    // Validate animation index
    if (currentAnimation.animationIndex < 0 || currentAnimation.animationIndex >= static_cast<int>(model->animations.size())) {
        LOG_ERROR("Invalid animation index: " + std::to_string(currentAnimation.animationIndex) +
                  " (valid range: 0-" + std::to_string(static_cast<int>(model->animations.size()) - 1) + ")");
        currentAnimation.playing = false;
        return;
    }

    currentAnimation.currentTime += deltaTime;

    // Handle looping
    if (currentAnimation.duration > 0.0f && currentAnimation.currentTime > currentAnimation.duration) {
        if (currentAnimation.looping) {
            currentAnimation.currentTime = std::fmod(currentAnimation.currentTime, currentAnimation.duration);
        } else {
            currentAnimation.currentTime = currentAnimation.duration;
            currentAnimation.playing = false;
        }
    }

    // Debug animation progress every second
    static float lastDebugTime = 0.0f;
    if (currentAnimation.currentTime - lastDebugTime > 1.0f) {
        LOG_TRACE("Animation progress: " + std::to_string(currentAnimation.currentTime) + "s / " + std::to_string(currentAnimation.duration) + "s");
        lastDebugTime = currentAnimation.currentTime;
    }
    
    // Apply animation to nodes
    const Gltf::Animation& animation = model->animations[currentAnimation.animationIndex];
    
    for (const auto& channel : animation.channels) {
        if (channel.target.node == -1 || channel.target.node >= static_cast<int>(nodeTransforms.size())) {
            LOG_WARNING("Invalid target node: " + std::to_string(channel.target.node));
            continue;
        }

        // Validate sampler index
        if (channel.sampler < 0 || channel.sampler >= static_cast<int>(animation.samplers.size())) {
            LOG_ERROR("Invalid sampler index: " + std::to_string(channel.sampler));
            continue;
        }

        const Gltf::AnimationSampler& sampler = animation.samplers[channel.sampler];

        // Validate accessor indices
        if (sampler.input < 0 || sampler.input >= static_cast<int>(model->accessors.size())) {
            LOG_ERROR("Invalid input accessor index: " + std::to_string(sampler.input));
            continue;
        }

        if (sampler.output < 0 || sampler.output >= static_cast<int>(model->accessors.size())) {
            LOG_ERROR("Invalid output accessor index: " + std::to_string(sampler.output));
            continue;
        }

        const Gltf::Accessor& inputAccessor = model->accessors[sampler.input];
        const Gltf::Accessor& outputAccessor = model->accessors[sampler.output];
        
        std::vector<float> times = extractTimeData(inputAccessor);
        
        if (channel.target.path == "translation") {
            std::vector<glm::vec3> values = extractVec3Data(outputAccessor);
            glm::vec3 newTranslation = interpolateVec3(times, values, currentAnimation.currentTime, sampler.interpolation);
            nodeTransforms[channel.target.node].translation = newTranslation;

            // Debug animation values
            static int debugCount = 0;
            if (debugCount < 5) {
                LOG_INFO("Node " + std::to_string(channel.target.node) + " translation: (" +
                        std::to_string(newTranslation.x) + ", " + std::to_string(newTranslation.y) + ", " + std::to_string(newTranslation.z) + ")");
                debugCount++;
            }
        } else if (channel.target.path == "rotation") {
            std::vector<glm::quat> values = extractQuatData(outputAccessor);
            glm::quat newRotation = interpolateQuat(times, values, currentAnimation.currentTime, sampler.interpolation);
            nodeTransforms[channel.target.node].rotation = newRotation;

            // Debug animation values
            static int debugCount = 0;
            if (debugCount < 5) {
                LOG_INFO("Node " + std::to_string(channel.target.node) + " rotation: (" +
                        std::to_string(newRotation.w) + ", " + std::to_string(newRotation.x) + ", " +
                        std::to_string(newRotation.y) + ", " + std::to_string(newRotation.z) + ")");
                debugCount++;
            }
        } else if (channel.target.path == "scale") {
            std::vector<glm::vec3> values = extractVec3Data(outputAccessor);
            glm::vec3 newScale = interpolateVec3(times, values, currentAnimation.currentTime, sampler.interpolation);
            nodeTransforms[channel.target.node].scale = newScale;

            // Debug animation values
            static int debugCount = 0;
            if (debugCount < 5) {
                LOG_INFO("Node " + std::to_string(channel.target.node) + " scale: (" +
                        std::to_string(newScale.x) + ", " + std::to_string(newScale.y) + ", " + std::to_string(newScale.z) + ")");
                debugCount++;
            }
        }
    }
    
    // Update bone matrices if there are skins
    if (!model->skins.empty()) {
        updateBoneMatrices(0); // Use first skin for now
    }
}

glm::mat4 AnimationManager::getNodeMatrix(int nodeIndex) const {
    if (nodeIndex < 0 || nodeIndex >= static_cast<int>(nodeTransforms.size())) {
        return glm::mat4(1.0f);
    }
    
    return nodeTransforms[nodeIndex].getMatrix();
}

void AnimationManager::updateBoneMatrices(int skinIndex) {
    if (skinIndex < 0 || skinIndex >= static_cast<int>(model->skins.size())) {
        return;
    }

    const Gltf::Skin& skin = model->skins[skinIndex];

    // Calculate global transforms for all nodes first
    std::vector<glm::mat4> globalTransforms(model->nodes.size(), glm::mat4(1.0f));

    // Process all nodes in order to ensure parents are processed before children
    for (size_t i = 0; i < model->nodes.size(); ++i) {
        const Gltf::Node& node = model->nodes[i];

        // Get local transform (animated or original)
        glm::mat4 localTransform;
        bool hasAnimation = (i < nodeTransforms.size() && !nodeTransforms[i].isIdentity());

        if (hasAnimation) {
            localTransform = nodeTransforms[i].getMatrix();
        } else {
            localTransform = getOriginalNodeTransform(node);
        }

        // Calculate global transform
        if (!node.children.empty()) {
            // This node has children, find its parent
            int parentIndex = -1;
            for (size_t j = 0; j < model->nodes.size(); ++j) {
                const auto& parentNode = model->nodes[j];
                for (int childIndex : parentNode.children) {
                    if (childIndex == static_cast<int>(i)) {
                        parentIndex = static_cast<int>(j);
                        break;
                    }
                }
                if (parentIndex != -1) break;
            }

            if (parentIndex != -1 && parentIndex < static_cast<int>(i)) {
                globalTransforms[i] = globalTransforms[parentIndex] * localTransform;
            } else {
                globalTransforms[i] = localTransform;
            }
        } else {
            // Check if this node is a child of another node
            int parentIndex = -1;
            for (size_t j = 0; j < model->nodes.size(); ++j) {
                const auto& parentNode = model->nodes[j];
                for (int childIndex : parentNode.children) {
                    if (childIndex == static_cast<int>(i)) {
                        parentIndex = static_cast<int>(j);
                        break;
                    }
                }
                if (parentIndex != -1) break;
            }

            if (parentIndex != -1 && parentIndex < static_cast<int>(i)) {
                globalTransforms[i] = globalTransforms[parentIndex] * localTransform;
            } else {
                globalTransforms[i] = localTransform;
            }
        }
    }

    for (size_t i = 0; i < skin.joints.size() && i < boneMatrices.size(); ++i) {
        int jointIndex = skin.joints[i];

        if (jointIndex >= 0 && jointIndex < static_cast<int>(globalTransforms.size())) {
            glm::mat4 globalTransform = globalTransforms[jointIndex];

            // Debug the transforms before multiplication
            LOG_TRACE("Joint " + std::to_string(jointIndex) + " global transform:");
            for (int row = 0; row < 4; ++row) {
                std::string rowStr = "  [";
                for (int col = 0; col < 4; ++col) {
                    rowStr += std::to_string(globalTransform[col][row]) + ", ";
                }
                rowStr += "]";
                LOG_TRACE(rowStr);
            }

            // Apply inverse bind matrix: finalMatrix = globalTransform * inverseBindMatrix
            if (i < inverseBindMatrices.size()) {
                LOG_TRACE("Joint " + std::to_string(jointIndex) + " inverse bind matrix:");
                for (int row = 0; row < 4; ++row) {
                    std::string rowStr = "  [";
                    for (int col = 0; col < 4; ++col) {
                        rowStr += std::to_string(inverseBindMatrices[i][col][row]) + ", ";
                    }
                    rowStr += "]";
                    LOG_TRACE(rowStr);
                }

                boneMatrices[i] = globalTransform * inverseBindMatrices[i];

                // Safety check for invalid values
                bool hasInvalidValues = false;
                for (int row = 0; row < 4; ++row) {
                    for (int col = 0; col < 4; ++col) {
                        float val = boneMatrices[i][col][row];
                        if (std::isnan(val) || std::isinf(val) || std::abs(val) > 1e6f) {
                            hasInvalidValues = true;
                            break;
                        }
                    }
                    if (hasInvalidValues) break;
                }

                if (hasInvalidValues) {
                    LOG_ERROR("Invalid bone matrix detected for joint " + std::to_string(jointIndex) + " - SKELETAL ANIMATION SYSTEM FAILURE");
                    LOG_ERROR("This indicates corrupted inverse bind matrices or node transforms");
                    LOG_ERROR("Global transform and inverse bind matrix shown above");
                    boneMatrices[i] = glm::mat4(1.0f);
                }
            } else {
                boneMatrices[i] = globalTransform;
            }
        } else {
            boneMatrices[i] = glm::mat4(1.0f);
        }
    }

    LOG_TRACE("Updated " + std::to_string(skin.joints.size()) + " bone matrices");
}

void AnimationManager::calculateGlobalTransforms(int nodeIndex, const glm::mat4& parentTransform, std::vector<glm::mat4>& globalTransforms) const {
    if (nodeIndex < 0 || nodeIndex >= static_cast<int>(model->nodes.size())) {
        LOG_ERROR("Invalid node index in calculateGlobalTransforms: " + std::to_string(nodeIndex));
        return;
    }

    const Gltf::Node& node = model->nodes[nodeIndex];

    // CRITICAL FIX: Use original node transform if no animation exists
    glm::mat4 localTransform;

    // Check if this node has animation data
    bool hasAnimation = (nodeIndex < static_cast<int>(nodeTransforms.size()) &&
                        !nodeTransforms[nodeIndex].isIdentity());

    if (hasAnimation) {
        // Use animated transform
        localTransform = nodeTransforms[nodeIndex].getMatrix();
        LOG_TRACE("Node " + std::to_string(nodeIndex) + " using ANIMATED transform");
    } else {
        // CRITICAL: Use original glTF node transform as fallback
        localTransform = getOriginalNodeTransform(node);
        LOG_TRACE("Node " + std::to_string(nodeIndex) + " using ORIGINAL transform");
    }

    // Debug local transform
    LOG_TRACE("Node " + std::to_string(nodeIndex) + " local transform:");
    for (int row = 0; row < 4; ++row) {
        std::string rowStr = "  [";
        for (int col = 0; col < 4; ++col) {
            rowStr += std::to_string(localTransform[col][row]) + ", ";
        }
        rowStr += "]";
        LOG_TRACE(rowStr);
    }

    // Calculate global transform
    globalTransforms[nodeIndex] = parentTransform * localTransform;

    // Check for invalid values in global transform
    bool hasInvalidGlobal = false;
    for (int row = 0; row < 4; ++row) {
        for (int col = 0; col < 4; ++col) {
            float val = globalTransforms[nodeIndex][col][row];
            if (std::isnan(val) || std::isinf(val) || std::abs(val) > 1e6f) {
                hasInvalidGlobal = true;
                break;
            }
        }
        if (hasInvalidGlobal) break;
    }

    if (hasInvalidGlobal) {
        LOG_ERROR("Invalid global transform calculated for node " + std::to_string(nodeIndex));
        globalTransforms[nodeIndex] = glm::mat4(1.0f);
    }

    // Recursively calculate for children
    for (int childIndex : node.children) {
        calculateGlobalTransforms(childIndex, globalTransforms[nodeIndex], globalTransforms);
    }
}

std::vector<float> AnimationManager::extractTimeData(const Gltf::Accessor& accessor) const {
    // Validate accessor indices
    if (accessor.bufferView < 0 || accessor.bufferView >= static_cast<int>(model->bufferViews.size())) {
        LOG_ERROR("Invalid bufferView index: " + std::to_string(accessor.bufferView));
        return std::vector<float>{0.0f, 1.0f};
    }

    const Gltf::BufferView& bufferView = model->bufferViews[accessor.bufferView];

    if (bufferView.buffer < 0 || bufferView.buffer >= static_cast<int>(model->buffers.size())) {
        LOG_ERROR("Invalid buffer index: " + std::to_string(bufferView.buffer));
        return std::vector<float>{0.0f, 1.0f};
    }

    const Gltf::Buffer& buffer = model->buffers[bufferView.buffer];

    // Validate buffer bounds
    size_t totalSize = bufferView.byteOffset + accessor.byteOffset + (accessor.count * sizeof(float));
    if (totalSize > buffer.data.size()) {
        LOG_ERROR("Time data buffer overflow! Required: " + std::to_string(totalSize) +
                 ", Available: " + std::to_string(buffer.data.size()));
        return std::vector<float>{0.0f, 1.0f}; // Return default time range
    }

    const float* data = reinterpret_cast<const float*>(
        buffer.data.data() + bufferView.byteOffset + accessor.byteOffset);

    std::vector<float> times;
    times.reserve(accessor.count);

    // Validate and copy time data
    for (size_t i = 0; i < accessor.count; ++i) {
        float timeValue = data[i];

        // Validate time value
        if (std::isnan(timeValue) || std::isinf(timeValue) || timeValue < 0.0f) {
            LOG_ERROR("Invalid time value at index " + std::to_string(i) + ": " + std::to_string(timeValue));
            timeValue = static_cast<float>(i); // Use index as fallback
        }

        times.push_back(timeValue);
    }

    // Debug the extracted time data
    LOG_TRACE("Extracted time data: count=" + std::to_string(accessor.count) +
              ", bufferView=" + std::to_string(accessor.bufferView) +
              ", byteOffset=" + std::to_string(accessor.byteOffset));

    for (size_t i = 0; i < std::min(times.size(), size_t(5)); ++i) {
        LOG_TRACE("Time[" + std::to_string(i) + "] = " + std::to_string(times[i]));
    }

    return times;
}

std::vector<glm::vec3> AnimationManager::extractVec3Data(const Gltf::Accessor& accessor) const {
    const Gltf::BufferView& bufferView = model->bufferViews[accessor.bufferView];
    const Gltf::Buffer& buffer = model->buffers[bufferView.buffer];
    
    const float* data = reinterpret_cast<const float*>(
        buffer.data.data() + bufferView.byteOffset + accessor.byteOffset);
    
    std::vector<glm::vec3> result;
    result.reserve(accessor.count);
    
    for (size_t i = 0; i < accessor.count; ++i) {
        result.emplace_back(data[i * 3], data[i * 3 + 1], data[i * 3 + 2]);
    }
    
    return result;
}

std::vector<glm::quat> AnimationManager::extractQuatData(const Gltf::Accessor& accessor) const {
    const Gltf::BufferView& bufferView = model->bufferViews[accessor.bufferView];
    const Gltf::Buffer& buffer = model->buffers[bufferView.buffer];
    
    const float* data = reinterpret_cast<const float*>(
        buffer.data.data() + bufferView.byteOffset + accessor.byteOffset);
    
    std::vector<glm::quat> result;
    result.reserve(accessor.count);
    
    for (size_t i = 0; i < accessor.count; ++i) {
        // glTF quaternions are stored as [x, y, z, w], glm expects [w, x, y, z]
        result.emplace_back(data[i * 4 + 3], data[i * 4], data[i * 4 + 1], data[i * 4 + 2]);
    }
    
    return result;
}

size_t AnimationManager::findKeyframeIndex(const std::vector<float>& times, float time) const {
    auto it = std::lower_bound(times.begin(), times.end(), time);
    if (it == times.end()) {
        return times.size() - 1;
    }
    if (it == times.begin()) {
        return 0;
    }
    return std::distance(times.begin(), it) - 1;
}

float AnimationManager::clamp(float value, float min, float max) const {
    return std::max(min, std::min(max, value));
}

glm::vec3 AnimationManager::interpolateVec3(const std::vector<float>& times, const std::vector<glm::vec3>& values, float time, const std::string& interpolation) {
    if (times.empty() || values.empty()) {
        return glm::vec3(0.0f);
    }

    if (times.size() == 1 || time <= times[0]) {
        return values[0];
    }

    if (time >= times.back()) {
        return values.back();
    }

    size_t index = findKeyframeIndex(times, time);
    if (index >= times.size() - 1) {
        return values.back();
    }

    float t0 = times[index];
    float t1 = times[index + 1];
    float factor = (time - t0) / (t1 - t0);

    if (interpolation == "STEP") {
        return values[index];
    } else if (interpolation == "LINEAR") {
        return glm::mix(values[index], values[index + 1], factor);
    } else if (interpolation == "CUBICSPLINE") {
        // Simplified cubic spline interpolation
        // For full implementation, we'd need tangent vectors
        return glm::mix(values[index], values[index + 1], factor);
    }

    return glm::mix(values[index], values[index + 1], factor);
}

glm::quat AnimationManager::interpolateQuat(const std::vector<float>& times, const std::vector<glm::quat>& values, float time, const std::string& interpolation) {
    if (times.empty() || values.empty()) {
        return glm::quat(1.0f, 0.0f, 0.0f, 0.0f);
    }

    if (times.size() == 1 || time <= times[0]) {
        return values[0];
    }

    if (time >= times.back()) {
        return values.back();
    }

    size_t index = findKeyframeIndex(times, time);
    if (index >= times.size() - 1) {
        return values.back();
    }

    float t0 = times[index];
    float t1 = times[index + 1];
    float factor = (time - t0) / (t1 - t0);

    if (interpolation == "STEP") {
        return values[index];
    } else {
        // Use spherical linear interpolation for quaternions
        return glm::slerp(values[index], values[index + 1], factor);
    }
}

// Helper method to get original node transform from glTF data
glm::mat4 AnimationManager::getOriginalNodeTransform(const Gltf::Node& node) const {
    // If node has a matrix directly, use that instead
    if (!node.matrix.empty() && node.matrix.size() >= 16) {
        // glTF matrices are column-major
        return glm::mat4(
            node.matrix[0], node.matrix[1], node.matrix[2], node.matrix[3],
            node.matrix[4], node.matrix[5], node.matrix[6], node.matrix[7],
            node.matrix[8], node.matrix[9], node.matrix[10], node.matrix[11],
            node.matrix[12], node.matrix[13], node.matrix[14], node.matrix[15]
        );
    }

    // Build transform matrix from TRS components (glm types)
    glm::mat4 transform = glm::mat4(1.0f);

    // Apply translation (glm::vec3)
    transform = glm::translate(transform, node.translation);

    // Apply rotation (glm::quat)
    transform = transform * glm::mat4_cast(node.rotation);

    // Apply scale (glm::vec3)
    transform = glm::scale(transform, node.scale);

    return transform;
}

void AnimationManager::setBindPose() {
    LOG_INFO("Setting model to bind pose (T-pose)");

    // Reset all node transforms to their original values
    for (size_t i = 0; i < nodeTransforms.size(); ++i) {
        if (i < model->nodes.size()) {
            const Gltf::Node& node = model->nodes[i];
            nodeTransforms[i].translation = node.translation;
            nodeTransforms[i].rotation = node.rotation;
            nodeTransforms[i].scale = node.scale;
        }
    }

    // Update bone matrices to bind pose
    if (!model->skins.empty()) {
        updateBoneMatrices(0);
        LOG_INFO("Bind pose set with " + std::to_string(boneMatrices.size()) + " bone matrices");
    }
}
