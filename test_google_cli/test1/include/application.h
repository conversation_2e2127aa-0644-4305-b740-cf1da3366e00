#ifndef APPLICATION_H
#define APPLICATION_H

#include <memory>
#include <string>

// Forward declaration for GLFW
struct GLFWwindow;

// Forward declarations
class Camera;
class ModelController;
class AnimationManager;
class Model;
class Shader;

namespace Gltf {
    struct GltfModel;
}

class Application {
public:
    Application(int width = 800, int height = 600, const std::string& title = "3D Engine");
    ~Application();

    // Application lifecycle
    bool initialize();
    void run();
    void shutdown();

    // Getters
    GLFWwindow* getWindow() const { return window; }
    Camera* getCamera() const { return camera.get(); }
    ModelController* getModelController() const { return modelController.get(); }
    int getWidth() const { return screenWidth; }
    int getHeight() const { return screenHeight; }

    // Window management
    void setWindowSize(int width, int height);
    bool shouldClose() const;

    // Static callback wrappers
    static void framebufferSizeCallback(GLFWwindow* window, int width, int height);
    static void mouseCallback(GLFWwindow* window, double xpos, double ypos);
    static void mouseButtonCallback(GLFWwindow* window, int button, int action, int mods);
    static void scrollCallback(GLFWwindow* window, double xoffset, double yoffset);
    static void keyCallback(GLFWwindow* window, int key, int scancode, int action, int mods);

private:
    // Window and OpenGL context
    GLFWwindow* window;
    int screenWidth, screenHeight;
    std::string windowTitle;

    // Core components
    std::unique_ptr<Camera> camera;
    std::unique_ptr<ModelController> modelController;
    std::unique_ptr<AnimationManager> animationManager;
    std::unique_ptr<Model> mainModel;
    std::unique_ptr<Shader> shader;

    // glTF model data (must persist for AnimationManager)
    std::unique_ptr<Gltf::GltfModel> gltfModel;

    // Application state
    bool initialized;
    float deltaTime;
    float lastFrame;

    // Mouse state for camera controls
    bool rightMousePressed;
    float lastX, lastY;
    bool firstMouse;

    // Private methods
    bool initializeGLFW();
    bool initializeOpenGL();
    bool loadShaders();
    bool loadModel();
    void setupCallbacks();
    void processInput();
    void update();
    void render();

    // Callback implementations
    void onFramebufferSizeChanged(int width, int height);
    void onMouseMoved(double xpos, double ypos);
    void onMouseButtonPressed(int button, int action, int mods);
    void onScrolled(double xoffset, double yoffset);
    void onKeyPressed(int key, int scancode, int action, int mods);

    // Helper methods
    void calculateDeltaTime();
    void printControls();

    // Static instance for callbacks
    static Application* instance;
};

#endif // APPLICATION_H
